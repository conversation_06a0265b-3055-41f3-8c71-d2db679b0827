defaults:
  - _self_
  - task: adroit_door_image

name: train_diffusion_unet_hybrid
_target_: diffusion_policy_3d.workspace.dp_workspace.DPWorkspace

task_name: ${task.name}
shape_meta: ${task.shape_meta}
exp_name: "debug"

horizon: 16
n_action_steps: 16

n_obs_steps: 2
n_latency_steps: 0
dataset_obs_steps: ${n_obs_steps}
keypoint_visible_rate: 1.0
obs_as_global_cond: True

use_image: true
policy:
  _target_: diffusion_policy_3d.policy.diffusion_image_policy.DiffusionImagePolicy
  
  condition_type: film
  diffusion_step_embed_dim: 128
  down_dims: [256, 512, 1024]
  horizon: ${horizon}
  kernel_size: 5
  n_action_steps: ${n_action_steps}
  n_groups: 8
  n_obs_steps: 2
 
  noise_scheduler:
    _target_: diffusers.schedulers.scheduling_ddim.DDIMScheduler
    num_train_timesteps: 50
    beta_start: 0.0001
    beta_end: 0.02
    beta_schedule: squaredcos_cap_v2
    clip_sample: True
    set_alpha_to_one: True
    steps_offset: 0
    prediction_type: epsilon # or sample
  num_inference_steps: 10
  obs_as_global_cond: true
  shape_meta: ${shape_meta}

  obs_encoder:
    _target_: diffusion_policy_3d.model.vision.timm_obs_encoder.TimmObsEncoder
    shape_meta: ${shape_meta}

    model_name: 'r3m'
    pretrained: True
    frozen: False

    global_pool: ''

    feature_aggregation: null
    position_encording: 'sinusoidal' # 'learnable' or 'sinusoidal'. it only works for transformer

    downsample_ratio: 32

    transforms:
      - type: RandomCrop
        ratio: 0.95
      - _target_: torchvision.transforms.RandomRotation
        degrees:
          - -5.0
          - 5.0
        expand: false
      - _target_: torchvision.transforms.ColorJitter
        brightness: 0.3
        contrast: 0.4
        saturation: 0.5
        hue: 0.08
     

    use_group_norm: True
    share_rgb_model: False
    imagenet_norm: True


ema:
  _target_: diffusion_policy_3d.model.diffusion.ema_model.EMAModel
  update_after_step: 0
  inv_gamma: 1.0
  power: 0.75
  min_value: 0.0
  max_value: 0.9999

dataloader:
  batch_size: 32
  num_workers: 8
  shuffle: True
  pin_memory: True
  persistent_workers: False

val_dataloader:
  batch_size: 32
  num_workers: 8
  shuffle: False
  pin_memory: True
  persistent_workers: False

optimizer:
  _target_: torch.optim.AdamW
  lr: 1.0e-4
  betas: [0.95, 0.999]
  eps: 1.0e-8
  weight_decay: 1.0e-6

training:
  device: "cuda:0"
  seed: 42
  debug: False
  resume: True
  lr_scheduler: cosine
  lr_warmup_steps: 500
  num_epochs: 301
  gradient_accumulate_every: 1
  use_ema: True
  rollout_every: 200
  checkpoint_every: 100
  val_every: 100
  sample_every: 5
  # steps per epoch
  max_train_steps: null
  max_val_steps: null
  # misc
  tqdm_interval_sec: 1.0
  save_video: False

logging:
  group: ${exp_name}
  id: null
  mode: online
  name: ${training.seed}
  project: humanoid_mimic
  resume: true
  tags:
  - train_diffusion_unet_hybrid
  - adroit

checkpoint:
  save_ckpt: False
  topk:
    monitor_key: test_mean_score
    mode: max
    k: 1
    format_str: 'epoch={epoch:04d}-test_mean_score={test_mean_score:.3f}.ckpt'
  save_last_ckpt: True
  save_last_snapshot: False

multi_run:
  run_dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
  wandb_name_base: ${now:%Y.%m.%d-%H.%M.%S}_${name}_${task_name}

hydra:
  job:
    override_dirname: ${name}
  run:
    dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
  sweep:
    dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
    subdir: ${hydra.job.num}
