run_3seed_qz2.sh
data/
# python related
*.egg-info/
*.pyc
.DS_Store
debug/
__pycache__/
scripts/plot/pdfs/
videos/
visualizations/
MinkowskiEngine/
video_dataset/
expert_ckpt/
demonstrations/
vrl3data/
trajectory_images/
dexdeform_flip_traj.pkl
dexdeform_folding_traj.pkl
trajectory_video.gif
trajectory_video.mp4
*.npy
isaacgym/
*.html
nohup.out
data/
_C.cpython-38-x86_64-linux-gnu.so
_C.cpython-39-x86_64-linux-gnu.so
*_temp.xml
real_robot_data/
output.gif
output.mp4
output.png

test.gif
test.mp4
test_depth.mp4
test.png

debug.png
debug.mp4
debug.gif

third_party/BEE/metaworld_expert_ckpt.zip
dexdeform_demonstrations.zip
personal_TASK.md
easytrain.sh
nohup.out
output/

bin
logs
wandb
outputs
data
data_local
.vscode
_wandb

**/.DS_Store

fuse.cfg

*.ai

# Generation results
results/

ray/auth.json

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/
