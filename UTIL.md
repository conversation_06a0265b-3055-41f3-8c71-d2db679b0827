# H5转zarr
bash convert_data.sh

# 查看数据


cd /home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy && python vis_dataset.py --dataset_path ../raw_pour_converted --use_img 1 --vis_cloud 1 --use_pc_color 1

# 训练
bash scripts/train_policy.sh idp3 gr1_dex-3d full_training 0 8 0.11

# 验证（在数据集上评估模型）
cd /home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy && python eval_model.py --config-name=idp3.yaml \
    hydra.run.dir=/home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy/data/outputs/gr1_dex-3d-idp3-full_training_seed0 \
    task.dataset.zarr_path=/home/<USER>/code/company/torqueidp3/data/raw_pour_converted \
    training.device="cuda:0"

